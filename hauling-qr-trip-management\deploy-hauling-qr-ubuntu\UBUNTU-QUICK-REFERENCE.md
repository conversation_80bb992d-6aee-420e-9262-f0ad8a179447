# Ubuntu VPS - Quick Reference Card
## Universal Ubuntu Deployment with GitHub PAT

### 🚀 Root User VPS (DigitalOcean, Linode, etc.)
```bash
# SSH as root
ssh root@YOUR_VPS_IP

# Deploy with GitHub PAT
git clone https://github.com/mightybadz18/hauling-qr-trip-management.git
cd hauling-qr-trip-management/deploy-hauling-qr-ubuntu
export GITHUB_PAT="*********************************************************************************************"
chmod +x auto-deploy.sh
sudo -E ./auto-deploy.sh
```

### 🇨🇦 Ubuntu User VPS (OVHCloud, AWS Ubuntu, etc.)
```bash
# SSH as ubuntu user
ssh ubuntu@YOUR_VPS_IP
# Password: vpsPasswordvps (for OVHCloud) or key-based auth

# Deploy with GitHub PAT + Permission Fix
git clone https://github.com/mightybadz18/hauling-qr-trip-management.git
cd hauling-qr-trip-management/deploy-hauling-qr-ubuntu
export GITHUB_PAT="ghp_your_github_personal_access_token_here"
chmod +x auto-deploy.sh
chmod +x fix-permissions-ubuntu-user.sh
sudo -E ./auto-deploy.sh
sudo ./fix-permissions-ubuntu-user.sh
```

### 📋 Essential Commands
```bash
# Check PM2 status
pm2 status

# View logs
pm2 logs hauling-qr-server

# Restart application
pm2 restart hauling-qr-server

# Check services
sudo systemctl status nginx postgresql

# Test application
curl http://localhost:5000/health
```

### 🌐 Access Points
- **Application**: `http://YOUR_OVHCLOUD_VPS_IP`
- **API Health**: `http://YOUR_OVHCLOUD_VPS_IP/api/health`
- **Admin**: `http://YOUR_OVHCLOUD_VPS_IP/admin`

### 🛠️ Troubleshooting
```bash
# Fix permissions
sudo ./fix-permissions-ubuntu-user.sh

# Check logs
tail -f /var/log/hauling-deployment/ovhcloud-deploy-*.log
pm2 logs hauling-qr-server

# Restart services
pm2 restart hauling-qr-server
sudo systemctl restart nginx
```

### 📁 Key Directories
- **Application**: `/var/www/hauling-qr-system/`
- **PM2 Config**: `/home/<USER>/.pm2/`
- **Logs**: `/var/log/hauling-deployment/`
- **Nginx Config**: `/etc/nginx/sites-available/hauling-qr-system`

### 🔧 Environment Variables
```bash
# For private repos
export GITHUB_PAT="your_token"

# For custom domain
export PRODUCTION_DOMAIN="truckhaul.top"

# Manual IP override
export MANUAL_IP="**************"
```

### ✅ Success Checklist
- [ ] SSH access with ubuntu user works
- [ ] `pm2 status` shows hauling-qr-server online
- [ ] `curl http://localhost:5000/health` returns OK
- [ ] Application accessible via browser
- [ ] All processes run as ubuntu user
- [ ] No permission errors in logs

### 🆘 Emergency Commands
```bash
# Kill all processes and restart
pm2 kill
sudo ./fix-permissions-ubuntu-user.sh

# Reset application
sudo rm -rf /var/www/hauling-qr-system
sudo -E ./ovhcloud-ubuntu-deploy.sh

# Check system resources
df -h
free -h
ps aux | grep node
```

---
**📞 Support**: Check `/var/log/hauling-deployment/` for detailed logs
**🎯 Goal**: Hauling QR Trip System running on OVHCloud VPS Canada with ubuntu user
