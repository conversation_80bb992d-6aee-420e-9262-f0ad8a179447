# Ubuntu VPS Deployment Guide - Hauling QR Trip System
## Universal Ubuntu Deployment with GitHub PAT Support

## 🎯 Overview

This guide provides comprehensive instructions for deploying the Hauling QR Trip System on **any Ubuntu VPS** with GitHub Personal Access Token (PAT) support for private repository access.

### ✨ Universal Ubuntu VPS Features
- ✅ **Universal Ubuntu Support** - Works on any Ubuntu VPS provider (DigitalOcean, AWS, Linode, Vultr, OVHCloud, etc.)
- ✅ **Flexible User Configuration** - Supports both root and ubuntu user deployments
- ✅ **GitHub PAT Integration** - Built-in private repository support
- ✅ **Automatic IP Detection** - Works with any VPS provider's IP assignment
- ✅ **PM2 Permission Handling** - Proper user-based PM2 configuration
- ✅ **Single Command Deployment** - `sudo -E ./auto-deploy.sh`
- ✅ **Development Configuration in Production** - Preserves all dev flexibility

### 🔧 Supported Configurations

#### **Configuration A: Root User Access (Traditional VPS)**
- **User Account**: `root` (direct root access)
- **VPS Providers**: DigitalOcean, AWS EC2, Linode, Vultr, etc.
- **Access Method**: `ssh root@YOUR_VPS_IP`
- **Permission Handling**: Automatic (root has all permissions)

#### **Configuration B: Ubuntu User Access (OVHCloud Style)**
- **User Account**: `ubuntu` (sudo access, no direct root)
- **VPS Providers**: OVHCloud VPS Canada, AWS Ubuntu AMI, etc.
- **Access Method**: `ssh ubuntu@YOUR_VPS_IP`
- **Permission Handling**: Requires `fix-permissions-ubuntu-user.sh` after deployment

## ⚡ Quick Start - Universal Ubuntu Deployment

### 🚀 Single Command Deployment

#### **For Root User VPS (DigitalOcean, Linode, etc.)**
```bash
# SSH as root
ssh root@YOUR_VPS_IP

# Deploy with GitHub PAT
git clone https://github.com/mightybadz18/hauling-qr-trip-management.git
cd hauling-qr-trip-management/deploy-hauling-qr-ubuntu
export GITHUB_PAT="*********************************************************************************************"
chmod +x auto-deploy.sh
sudo -E ./auto-deploy.sh
```

#### **For Ubuntu User VPS (OVHCloud, AWS Ubuntu, etc.)**
```bash
# SSH as ubuntu user
ssh ubuntu@YOUR_VPS_IP
# Password: vpsPasswordvps (for OVHCloud) or key-based auth

# Deploy with GitHub PAT + Permission Fix
git clone https://github.com/mightybadz18/hauling-qr-trip-management.git
mkdir hauling-qr-trip/deploy
cd hauling-qr-trip/deploy
export GITHUB_PAT="*********************************************************************************************"
chmod +x auto-deploy.sh
chmod +x fix-permissions-ubuntu-user.sh
sudo -E ./auto-deploy.sh
sudo ./fix-permissions-ubuntu-user.sh
```

### � GitHub PAT Setup (Required for Private Repository)

#### **Creating a GitHub Personal Access Token**
1. Go to GitHub → Settings → Developer settings → Personal access tokens → Tokens (classic)
2. Click "Generate new token (classic)"
3. Set expiration and select scopes:
   - ✅ `repo` (Full control of private repositories)
   - ✅ `read:org` (Read org and team membership)
4. Copy the generated token (starts with `ghp_`)

#### **Using Your GitHub PAT**
```bash
# Set your GitHub PAT (replace with your actual token)
export GITHUB_PAT="*********************************************************************************************"

# Verify PAT is set correctly
echo "GitHub PAT length: ${#GITHUB_PAT}"
# Should show: GitHub PAT length: 40
```

## 📁 Deployment File Structure

### Core Deployment Scripts
- **`auto-deploy.sh`** - **PRIMARY** - Main deployment script with GitHub PAT support
- **`fix-permissions-ubuntu-user.sh`** - **UBUNTU USER ONLY** - Permission fix for ubuntu user deployments

### Documentation
- **`UBUNTU_VPS_DEPLOYMENT_GUIDE.md`** - This comprehensive universal guide
- **`UBUNTU-QUICK-REFERENCE.md`** - Quick reference commands for all Ubuntu VPS types
- **`CLOUDFLARE_DNS_SETUP_GUIDE.md`** - DNS configuration with Cloudflare
- **`TROUBLESHOOTING_GUIDE.md`** - Troubleshooting solutions

---

## 📋 Prerequisites

### Universal VPS Requirements
- **OS**: Ubuntu 24.04 LTS (recommended) or 22.04 LTS
- **RAM**: Minimum 2GB (4GB recommended)
- **Storage**: Minimum 20GB SSD
- **Network**: Public IP with ports 22, 80, 443 accessible
- **User Access**: Either root user OR ubuntu user with sudo privileges

### GitHub Requirements
- **GitHub Personal Access Token (PAT)** with `repo` scope
- **Private Repository Access** (if using private repository)

### Local Requirements
- SSH client to connect to your VPS
- Domain name (optional but recommended - e.g., truckhaul.top)

---

## 🌐 Step 1: VPS Server Setup

### 1.1 Server Access

#### **For Root User VPS (DigitalOcean, Linode, etc.)**
```bash
# Connect as root user
ssh root@YOUR_VPS_IP

# Verify root access
whoami
# Output: root
```

#### **For Ubuntu User VPS (OVHCloud, AWS Ubuntu, etc.)**
```bash
# Connect as ubuntu user
ssh ubuntu@YOUR_VPS_IP
# Password: vpsPasswordvps (for OVHCloud) or key-based auth

# Verify ubuntu user and sudo access
whoami
# Output: ubuntu

sudo whoami
# Output: root (confirms sudo access)
```

### 1.2 Basic System Update
```bash
# Update package repositories (works for both root and ubuntu user)
sudo apt update && sudo apt upgrade -y

# Install essential tools
sudo apt install -y curl git wget unzip build-essential

# Verify Git is installed
git --version
```

### 1.3 Network Verification
```bash
# Check public IP (works with any VPS provider)
curl -4 ifconfig.me
# Should return your VPS public IP

# Test internet connectivity
ping -c 3 google.com

# Check available disk space
df -h
```

---

## 🔧 Step 2: Download and Prepare Deployment Script

### 2.1 Clone Repository (Universal)
```bash
# Clone the repository (works for both root and ubuntu user)
git clone https://github.com/mightybadz18/hauling-qr-trip-management.git
cd hauling-qr-trip-management/deploy-hauling-qr-ubuntu

# Verify deployment files
ls -la
# Should show: auto-deploy.sh, fix-permissions-ubuntu-user.sh

# Make scripts executable
chmod +x auto-deploy.sh
chmod +x fix-permissions-ubuntu-user.sh
```

### 2.2 Set Environment Variables
```bash
# Required: GitHub PAT for private repository access
export GITHUB_PAT="ghp_your_github_personal_access_token_here"

# Optional: Custom domain
export PRODUCTION_DOMAIN="truckhaul.top"

# Optional: Manual IP override (only if auto-detection fails)
export MANUAL_IP="YOUR_VPS_IP"  # Your actual VPS IP

# Verify environment variables are set
echo "GitHub PAT length: ${#GITHUB_PAT}"
echo "Production domain: $PRODUCTION_DOMAIN"
```

---

## 🚀 Step 3: Universal Ubuntu Deployment

### 3.1 Deploy with auto-deploy.sh (Primary Method)

#### **For Root User VPS (DigitalOcean, Linode, etc.)**
```bash
# Single command deployment
sudo -E ./auto-deploy.sh

# With custom domain
sudo -E PRODUCTION_DOMAIN=truckhaul.top ./auto-deploy.sh

# Full configuration (RECOMMENDED)
sudo -E GITHUB_PAT=your_token PRODUCTION_DOMAIN=truckhaul.top ./auto-deploy.sh
```

#### **For Ubuntu User VPS (OVHCloud, AWS Ubuntu, etc.)**
```bash
# Step 1: Run main deployment
sudo -E ./auto-deploy.sh

# Step 2: Fix ubuntu user permissions (CRITICAL)
sudo ./fix-permissions-ubuntu-user.sh

# OR: Full configuration with permission fix
sudo -E GITHUB_PAT=your_token PRODUCTION_DOMAIN=truckhaul.top ./auto-deploy.sh
sudo ./fix-permissions-ubuntu-user.sh
```

### 3.2 What auto-deploy.sh Does (Universal)

The `auto-deploy.sh` script performs comprehensive deployment operations:

1. **🔍 IP Detection** - Automatically detects your VPS IP address (any provider)
2. **📦 System Dependencies** - Installs Node.js 20.x, PostgreSQL, Nginx, PM2
3. **🔥 Firewall Configuration** - Configures UFW firewall (ports 22, 80, 443, 5000)
4. **� GitHub Authentication** - Uses GitHub PAT to clone private repository
5. **⚙️ Environment Configuration** - Creates production .env with detected settings
6. **🗄️ Database Setup** - Creates PostgreSQL database and runs migrations
7. **🏗️ Application Build** - Builds React frontend and Node.js backend
8. **🌐 Nginx Configuration** - Configures reverse proxy and static file serving
9. **🚀 PM2 Configuration** - Sets up PM2 process management
10. **🏥 Health Checks** - Verifies deployment success

### 3.3 Ubuntu User Permission Fix (Required for Ubuntu User VPS)

The `fix-permissions-ubuntu-user.sh` script handles ubuntu user-specific requirements:

1. **🛑 Process Management** - Stops all running Node.js and PM2 processes
2. **📁 File Ownership** - Sets ubuntu user ownership for application directory
3. **🔧 PM2 Directory** - Creates and configures PM2 directory for ubuntu user
4. **⚙️ PM2 Configuration** - Creates ecosystem.config.js with proper paths
5. **🚀 PM2 Startup** - Configures PM2 to start as ubuntu user on boot
6. **✅ Verification** - Checks process ownership and application health

#### Critical Files/Folders Handled by Permission Script:
```bash
# Application Directory (CRITICAL)
/var/www/hauling-qr-system/          # Main application directory
├── server/                          # Backend server files
├── client/                          # Frontend React files
├── database/                        # Database migration files
├── node_modules/                    # Dependencies (if installed)
└── ecosystem.config.js              # PM2 configuration

# PM2 Directory (CRITICAL)
/home/<USER>/.pm2/                   # PM2 process manager directory
├── logs/                            # PM2 log files
├── pids/                            # Process ID files
└── dump.pm2                         # PM2 process dump

# Node.js Processes
- All Node.js processes run as ubuntu user
- PM2 startup service configured for ubuntu user
- Process ownership verified after deployment
```

#### Permission Fix Script Usage:
```bash
# For Ubuntu User VPS ONLY (OVHCloud, AWS Ubuntu, etc.)
# ALWAYS run after auto-deploy.sh:
sudo ./fix-permissions-ubuntu-user.sh

# This script automatically fixes:
# ✅ PM2 ownership issues (critical for process management)
# ✅ File permissions for ubuntu user
# ✅ Node.js process ownership
# ✅ Application directory permissions
# ✅ PM2 startup configuration for ubuntu user
```
# ✅ Log file access permissions
# ✅ PM2 startup configuration for ubuntu user
```

#### Manual Permission Fix (if script fails):
```bash
# Fix application directory ownership
sudo chown -R ubuntu:ubuntu /var/www/hauling-qr-system
sudo chmod -R 755 /var/www/hauling-qr-system

# Fix PM2 directory ownership
sudo chown -R ubuntu:ubuntu /home/<USER>/.pm2
sudo chmod -R 755 /home/<USER>/.pm2

# Fix log directory permissions
sudo chown -R ubuntu:ubuntu /var/log/hauling-deployment
```

---

## 🌍 Step 4: DNS Configuration for OVHCloud VPS Canada

### 4.1 OVHCloud VPS Canada DNS Setup
Point your domain to your OVHCloud VPS Canada IP:
```
A Record: @ → YOUR_DETECTED_VPS_IP
A Record: www → YOUR_DETECTED_VPS_IP
```

### 4.2 Cloudflare DNS Setup (Recommended)
1. **Add Domain to Cloudflare**
   - Sign up at cloudflare.com
   - Add your domain (e.g., truckhaul.top)
   - Update nameservers at your domain registrar

2. **Configure DNS Records**
   ```
   Type: A
   Name: @
   Content: YOUR_DETECTED_VPS_IP
   Proxy: ✅ (Orange Cloud)
   
   Type: A  
   Name: www
   Content: YOUR_DETECTED_VPS_IP
   Proxy: ✅ (Orange Cloud)
   ```

3. **SSL/TLS Settings**
   - Go to SSL/TLS → Overview
   - Set to "Full" (not "Full Strict")
   - Enable "Always Use HTTPS"

4. **Page Rules for React SPA**
   ```
   URL: yourdomain.com/*
   Settings: 
   - Cache Level: Standard
   - Browser Cache TTL: 4 hours
   ```

---

## ✅ Step 5: Verification Checklist

### 5.1 Automatic Verification
The deployment script automatically verifies:
- ✅ PM2 process is running
- ✅ Backend health endpoint responds
- ✅ Nginx serves frontend
- ✅ CORS configuration works for IP and domain
- ✅ Database connectivity

### 5.2 Manual Verification
```bash
# Check PM2 status
pm2 status

# Check Nginx status  
systemctl status nginx

# Check PostgreSQL status
systemctl status postgresql

# Test backend directly
curl http://localhost:5000/health

# Test frontend
curl http://localhost/

# Check logs
pm2 logs hauling-qr-server
tail -f /var/log/hauling-deployment/auto-deploy-*.log
```

### 5.3 Browser Testing
1. **IP Access**: `http://YOUR_DETECTED_VPS_IP`
2. **Domain Access**: `https://yourdomain.com`
3. **API Testing**: `https://yourdomain.com/api/health`

---

## 🔧 Step 6: Post-Deployment Management

### 6.1 PM2 Commands
```bash
# View application status
pm2 status

# View logs
pm2 logs hauling-qr-server

# Restart application
pm2 restart hauling-qr-server

# Stop application
pm2 stop hauling-qr-server

# View detailed info
pm2 show hauling-qr-server
```

### 6.2 Nginx Commands
```bash
# Check Nginx status
systemctl status nginx

# Restart Nginx
systemctl restart nginx

# Test Nginx configuration
nginx -t

# View Nginx logs
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
```

### 6.3 Database Commands
```bash
# Connect to database
sudo -u postgres psql -d hauling_qr_system

# Check database status
systemctl status postgresql

# View database logs
tail -f /var/log/postgresql/postgresql-*.log
```

---

## 🔄 Step 7: VPS Migration Guide

### 7.1 Migrating to New VPS
1. **Prepare New VPS** with Ubuntu 24.04
2. **Run Deployment Script** - IP will be auto-detected
   ```bash
   sudo -E ./auto-deploy-enhanced.sh
   ```
3. **Update DNS Records** to point to new IP
4. **Test New Deployment** before switching traffic

### 7.2 IP Address Changes
If your VPS IP changes:
```bash
# Re-run deployment to update IP configuration
sudo -E ./auto-deploy-enhanced.sh

# Or manually update environment
export MANUAL_IP="new.ip.address"
sudo -E ./auto-deploy-enhanced.sh
```

---

## 🛠️ OVHCloud VPS Canada Troubleshooting Guide

### OVHCloud-Specific Issues and Solutions

#### 1. OVHCloud IP Detection Issues
```bash
# Check OVHCloud VPS public IP
curl -4 ifconfig.me
# Should return your OVHCloud Canada IP (e.g., **************)

# Manual IP override for OVHCloud
export MANUAL_IP="**************"  # Your actual OVHCloud VPS IP
sudo -E ./auto-deploy.sh
```

#### 2. Ubuntu User Permission Issues (CRITICAL)
```bash
# If PM2 processes fail or run as root, fix permissions:
sudo ./fix-permissions-ubuntu-user.sh

# Check if processes are running as ubuntu user
ps aux | grep node
# Should show ubuntu user, not root

# Fix PM2 ownership manually if needed
sudo chown -R ubuntu:ubuntu /home/<USER>/.pm2
sudo chown -R ubuntu:ubuntu /var/www/hauling-qr-system
```

#### 3. GitHub Clone Fails on OVHCloud
```bash
# Test GitHub connectivity from OVHCloud VPS
curl -I https://github.com

# Set GitHub PAT for private repositories
export GITHUB_PAT="your_github_personal_access_token"
sudo -E ./auto-deploy.sh

# If still failing, check OVHCloud firewall settings
sudo ufw status
```

#### 4. PostgreSQL Issues on OVHCloud
```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Restart PostgreSQL with proper permissions
sudo systemctl restart postgresql

# Check PostgreSQL logs
sudo tail -f /var/log/postgresql/postgresql-*.log

# Connect to database as ubuntu user
sudo -u postgres psql -d hauling_qr_system
```

#### 5. PM2 Process Issues (Ubuntu User Specific)
```bash
# Check PM2 status as ubuntu user
pm2 status

# If PM2 not found, install globally for ubuntu user
npm install -g pm2

# Check PM2 logs
pm2 logs hauling-qr-server

# Restart PM2 processes
pm2 restart hauling-qr-server

# Fix PM2 startup for ubuntu user
pm2 startup systemd -u ubuntu --hp /home/<USER>
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u ubuntu --hp /home/<USER>
```

#### 6. Nginx Configuration Issues on OVHCloud
The deployment uses development-style CORS configuration that should work with:
- Domain access: `https://yourdomain.com`
- IP access: `http://your.vps.ip`
- Localhost: `http://localhost:3000`

If CORS issues persist:
```bash
# Check server logs for CORS messages
pm2 logs hauling-qr-server | grep -i cors

# Verify environment variables
cat /var/www/hauling-qr-system/.env | grep -E "(DETECTED_VPS_IP|PRODUCTION_DOMAIN|DEV_ENABLE_CORS_ALL)"
```

---

## 📊 Configuration Details

### Environment Configuration
The deployment creates a production environment that mirrors development settings:

```bash
# Key configuration preserved from development:
NODE_ENV=production                    # Only change from development
AUTO_DETECT_IP=true                   # Preserved
DEV_ENABLE_CORS_ALL=true             # Preserved  
DEV_DISABLE_RATE_LIMITING=true       # Preserved
DETECTED_VPS_IP=auto-detected-ip     # Auto-populated
PRODUCTION_DOMAIN=yourdomain.com     # Configurable
```

### Service Ports
- **Frontend**: Port 3000 (served by Nginx on port 80/443)
- **Backend**: Port 5000 (proxied by Nginx)
- **Database**: Port 5432 (PostgreSQL, localhost only)
- **Nginx**: Port 80 (HTTP) and 443 (HTTPS via Cloudflare)

### File Locations
- **Application**: `/var/www/hauling-qr-system/`
- **Logs**: `/var/log/hauling-deployment/`
- **Nginx Config**: `/etc/nginx/sites-available/hauling-qr-system`
- **PM2 Config**: `/var/www/hauling-qr-system/ecosystem.config.js`

---

## 🎯 Success Indicators

After successful deployment, you should see:

1. **✅ Deployment Summary** with detected IP and URLs
2. **✅ All Services Running** (PostgreSQL, PM2, Nginx)
3. **✅ Health Checks Passing** (Backend, Frontend, CORS)
4. **✅ Application Accessible** via both IP and domain
5. **✅ Development-Style Flexibility** preserved in production

The system will behave identically to development mode for CORS, networking, and user experience while maintaining proper production logging and security contexts.

---

## 📞 Support

For deployment issues:
1. Check the deployment log: `/var/log/hauling-deployment/auto-deploy-*.log`
2. Review PM2 logs: `pm2 logs hauling-qr-server`
3. Verify Nginx configuration: `nginx -t`
4. Test health endpoints manually
5. Ensure DNS is properly configured

The enhanced deployment script provides comprehensive error handling and detailed logging to help diagnose any issues that may occur during deployment.

---

## 🇨🇦 OVHCloud VPS Canada Deployment Summary

### ✅ What You Have After Successful Deployment

#### 🖥️ OVHCloud VPS Canada Configuration
- **VPS Provider**: OVHCloud Canada
- **User Account**: `ubuntu` (password: `vpsPasswordvps`)
- **IP Address**: Auto-detected (e.g., **************)
- **Operating System**: Ubuntu 24.04 LTS
- **Access Method**: SSH with ubuntu user (no root required)

#### 📁 File Structure and Permissions
```
/var/www/hauling-qr-system/          # Application (ubuntu:ubuntu)
/home/<USER>/.pm2/                   # PM2 processes (ubuntu:ubuntu)
/var/log/hauling-deployment/         # Deployment logs (ubuntu:ubuntu)
/etc/nginx/sites-available/          # Nginx config (root:root)
/var/log/nginx/                      # Nginx logs (www-data:adm)
```

#### 🚀 Running Services
- **PM2**: `hauling-qr-server` running as ubuntu user
- **Nginx**: Reverse proxy on ports 80/443
- **PostgreSQL**: Database server on port 5432
- **UFW Firewall**: Ports 22, 80, 443, 5000 open

#### 🔧 Key Commands for OVHCloud VPS Management
```bash
# Check PM2 status (as ubuntu user)
pm2 status

# View application logs
pm2 logs hauling-qr-server

# Restart application
pm2 restart hauling-qr-server

# Check system services
sudo systemctl status nginx
sudo systemctl status postgresql

# Fix permissions if needed
sudo ./fix-permissions-ubuntu-user.sh
```

#### 🌐 Access URLs
- **Direct IP Access**: `http://YOUR_OVHCLOUD_VPS_IP`
- **Domain Access**: `https://truckhaul.top` (if configured)
- **API Health Check**: `http://YOUR_OVHCLOUD_VPS_IP/api/health`
- **Admin Dashboard**: `http://YOUR_OVHCLOUD_VPS_IP/admin`

### 🎯 Success Indicators for OVHCloud VPS Canada
1. ✅ **SSH Access**: Can connect with `ssh ubuntu@YOUR_IP`
2. ✅ **PM2 Running**: `pm2 status` shows `hauling-qr-server` online
3. ✅ **Application Responding**: `curl http://localhost:5000/health` returns OK
4. ✅ **Nginx Serving**: `curl http://localhost/` returns React app
5. ✅ **Database Connected**: No database connection errors in logs
6. ✅ **Ubuntu User Ownership**: All processes run as ubuntu user
7. ✅ **Firewall Configured**: UFW allows necessary ports
8. ✅ **Domain Working**: (if configured) Domain resolves to application

### 📞 OVHCloud VPS Canada Support Checklist
If you encounter issues:
1. **Check deployment logs**: `/var/log/hauling-deployment/ovhcloud-deploy-*.log`
2. **Verify ubuntu user permissions**: `ls -la /var/www/hauling-qr-system`
3. **Check PM2 processes**: `pm2 status` and `pm2 logs`
4. **Test OVHCloud connectivity**: `curl -4 ifconfig.me`
5. **Run permission fix**: `sudo ./fix-permissions-ubuntu-user.sh`
6. **Verify services**: `sudo systemctl status nginx postgresql`

**🎉 Your Hauling QR Trip System is now successfully deployed on OVHCloud VPS Canada!**